<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="GATv2多头注意力机制与信息聚合过程" id="gatv2-multihead-attention">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="1000" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 标题 -->
        <mxCell id="title" value="GATv2多头注意力机制与信息聚合过程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="500" height="30" as="geometry" />
        </mxCell>

        <!-- 第一阶段：RSSI差分特征处理 -->
        <mxCell id="stage1_title" value="阶段1：RSSI差分特征处理与环境干扰消除" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="70" width="350" height="40" as="geometry" />
        </mxCell>

        <!-- 原始RSSI信号 -->
        <mxCell id="raw_rssi" value="原始RSSI信号&#xa;$\text{RSSI}^{(t,a_i)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="70" y="130" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 差分特征计算 -->
        <mxCell id="diff_calculation" value="差分特征计算&#xa;$\Delta\text{RSSI}_{i,j} = \text{RSSI}_i - \text{RSSI}_j$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="130" width="150" height="50" as="geometry" />
        </mxCell>

        <!-- 第二阶段：异构图动态拓扑构建 -->
        <mxCell id="stage2_title" value="阶段2：异构图动态拓扑构建" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="70" width="300" height="40" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node" value="标签节点&#xa;$\mathbf{h}_i^{tag}$" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="470" y="130" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node" value="天线节点&#xa;$\mathbf{h}_j^{antenna}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="580" y="130" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 四种边关系 -->
        <mxCell id="edge_types" value="四种边关系类型：&#xa;• T-T: 标签-标签 (虚线)&#xa;• T-A: 标签-天线 (实线)&#xa;• A-T: 天线-标签 (实线)&#xa;• A-A: 天线-天线 (点线)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="470" y="210" width="190" height="80" as="geometry" />
        </mxCell>

        <!-- 自适应K近邻算法 -->
        <mxCell id="knn_algorithm" value="自适应K近邻算法&#xa;$K_i = \max(K_{min}, \min(K_{max}, K_{base} + \lfloor \beta \cdot \log(1 + \rho_i) \rfloor))$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="680" y="130" width="200" height="60" as="geometry" />
        </mxCell>

        <!-- 第三阶段：三层GATv2网络架构 -->
        <mxCell id="stage3_title" value="阶段3：三层GATv2网络架构" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fad7ac;strokeColor=#b46504;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="320" width="300" height="40" as="geometry" />
        </mxCell>

        <!-- 第一层：多头注意力 -->
        <mxCell id="layer1_title" value="第一层 (H=4个头)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="70" y="380" width="150" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力头1 -->
        <mxCell id="head1_1" value="Head 1&#xa;$\alpha_{ij}^{(1,1)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力头2 -->
        <mxCell id="head1_2" value="Head 2&#xa;$\alpha_{ij}^{(1,2)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="120" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力头3 -->
        <mxCell id="head1_3" value="Head 3&#xa;$\alpha_{ij}^{(1,3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="190" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力头4 -->
        <mxCell id="head1_4" value="Head 4&#xa;$\alpha_{ij}^{(1,4)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="260" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 第一层拼接 -->
        <mxCell id="concat1" value="拼接操作&#xa;$\mathbf{h}_i^{(1)} = \text{Concat}(\mathbf{h}_i^{(1,1)}, ..., \mathbf{h}_i^{(1,4)})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="490" width="270" height="40" as="geometry" />
        </mxCell>

        <!-- 第二层：多头注意力 -->
        <mxCell id="layer2_title" value="第二层 (H=4个头)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="380" width="150" height="30" as="geometry" />
        </mxCell>

        <!-- 第二层注意力头 -->
        <mxCell id="head2_1" value="Head 1&#xa;$\alpha_{ij}^{(2,1)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head2_2" value="Head 2&#xa;$\alpha_{ij}^{(2,2)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head2_3" value="Head 3&#xa;$\alpha_{ij}^{(2,3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="520" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head2_4" value="Head 4&#xa;$\alpha_{ij}^{(2,4)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="590" y="430" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 第二层拼接 -->
        <mxCell id="concat2" value="拼接操作&#xa;$\mathbf{h}_i^{(2)} = \text{Concat}(\mathbf{h}_i^{(2,1)}, ..., \mathbf{h}_i^{(2,4)})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="490" width="270" height="40" as="geometry" />
        </mxCell>

        <!-- 第三层：单头注意力 -->
        <mxCell id="layer3_title" value="第三层 (H=1个头)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="730" y="380" width="150" height="30" as="geometry" />
        </mxCell>

        <!-- 第三层单头 -->
        <mxCell id="head3_1" value="Single Head&#xa;$\alpha_{ij}^{(3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="760" y="430" width="90" height="40" as="geometry" />
        </mxCell>

        <!-- 第三层输出 -->
        <mxCell id="layer3_output" value="最终特征&#xa;$\mathbf{h}_i^{(3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="760" y="490" width="90" height="40" as="geometry" />
        </mxCell>

        <!-- 第四阶段：信号可靠性调节与残差连接 -->
        <mxCell id="stage4_title" value="阶段4：信号可靠性调节与残差连接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="570" width="350" height="40" as="geometry" />
        </mxCell>

        <!-- 可靠性评分计算 -->
        <mxCell id="reliability_calculation" value="可靠性评分计算&#xa;$\rho_i = \frac{1}{1 + \sigma^2(\text{RSSI}_i)}$" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fad7ac;strokeColor=#b46504;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="70" y="630" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 特征调节 -->
        <mxCell id="feature_adjustment" value="特征调节&#xa;$\mathbf{h}_{adj} = \mathbf{h}_i \cdot (\alpha_l + (1-\alpha_l) \cdot \rho_i)$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="240" y="630" width="180" height="60" as="geometry" />
        </mxCell>

        <!-- 残差连接 -->
        <mxCell id="residual_connection" value="残差连接与层归一化&#xa;$\mathbf{h}_{out} = \text{LayerNorm}(\mathbf{h}_{adj} + \mathbf{h}_{input})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="450" y="630" width="200" height="60" as="geometry" />
        </mxCell>

        <!-- 第五阶段：多模型融合 -->
        <mxCell id="stage5_title" value="阶段5：多模型融合定位预测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="570" width="300" height="40" as="geometry" />
        </mxCell>

        <!-- MLP初步预测 -->
        <mxCell id="mlp_prediction" value="MLP初步预测&#xa;$\hat{\mathbf{p}}_{mlp} = f_{MLP}(\mathbf{x}_{diff})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="770" y="630" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- 先验融合 -->
        <mxCell id="prior_fusion" value="先验融合&#xa;$\mathbf{h}_{enhanced} = f_{fusion}(\mathbf{h}_{tag}, \hat{\mathbf{p}}_{mlp})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="920" y="630" width="140" height="50" as="geometry" />
        </mxCell>

        <!-- 最终融合预测 -->
        <mxCell id="final_fusion" value="最终融合预测&#xa;$\hat{\mathbf{p}}_{fusion} = w \cdot \hat{\mathbf{p}}_{hetero} + (1-w) \cdot \hat{\mathbf{p}}_{mlp}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="800" y="720" width="220" height="50" as="geometry" />
        </mxCell>

        <!-- GATv2注意力权重计算公式 -->
        <mxCell id="attention_formula" value="GATv2注意力权重计算：&#xa;$\alpha_{ij}^{(r,h)} = \frac{\exp(\text{LeakyReLU}(\mathbf{a}_r^T \cdot \mathbf{W}_r [\mathbf{h}_i \| \mathbf{h}_j \| \mathbf{e}_{ij}]))}{\sum_{k \in \mathcal{N}_i^{(r)}} \exp(\text{LeakyReLU}(\mathbf{a}_r^T \cdot \mathbf{W}_r [\mathbf{h}_i \| \mathbf{h}_k \| \mathbf{e}_{ik}]))}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="720" width="500" height="50" as="geometry" />
        </mxCell>

        <!-- 关键创新点标注 -->
        <mxCell id="innovation1" value="创新点1：&#xa;RSSI差分特征&#xa;消除环境干扰" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="200" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="innovation2" value="创新点2：&#xa;异构图建模&#xa;四种边关系" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="300" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="innovation3" value="创新点3：&#xa;三层递减架构&#xa;多头到单头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="550" width="100" height="60" as="geometry" />
        </mxCell>

        <mxCell id="innovation4" value="创新点4：&#xa;信号可靠性调节&#xa;自适应特征增强" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="710" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="innovation5" value="创新点5：&#xa;双阶段融合&#xa;MLP+异构图" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1080" y="630" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 主要连接线 -->
        <!-- 阶段1：RSSI处理流程 -->
        <mxCell id="edge1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="raw_rssi" target="diff_calculation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="170" y="155" as="sourcePoint" />
            <mxPoint x="200" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 阶段2：异构图构建 -->
        <mxCell id="edge2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="diff_calculation" target="tag_node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="155" as="sourcePoint" />
            <mxPoint x="470" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 异构图到K近邻 -->
        <mxCell id="edge3" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="antenna_node" target="knn_algorithm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="160" as="sourcePoint" />
            <mxPoint x="680" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 阶段3：三层网络流程 -->
        <mxCell id="edge4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="tag_node" target="layer1_title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="190" as="sourcePoint" />
            <mxPoint x="145" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第一层内部连接 -->
        <mxCell id="edge5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head1_1" target="concat1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="80" y="470" as="sourcePoint" />
            <mxPoint x="185" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第一层到第二层 -->
        <mxCell id="edge6" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="concat1" target="layer2_title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="510" as="sourcePoint" />
            <mxPoint x="400" y="395" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第二层内部连接 -->
        <mxCell id="edge7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head2_1" target="concat2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="470" as="sourcePoint" />
            <mxPoint x="515" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第二层到第三层 -->
        <mxCell id="edge8" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="concat2" target="layer3_title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="510" as="sourcePoint" />
            <mxPoint x="730" y="395" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第三层内部连接 -->
        <mxCell id="edge9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head3_1" target="layer3_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="805" y="470" as="sourcePoint" />
            <mxPoint x="805" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 阶段4和5的连接 -->
        <mxCell id="edge10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="layer3_output" target="stage4_title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="805" y="530" as="sourcePoint" />
            <mxPoint x="225" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 可靠性调节流程 -->
        <mxCell id="edge11" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="reliability_calculation" target="feature_adjustment">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="660" as="sourcePoint" />
            <mxPoint x="240" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge12" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="feature_adjustment" target="residual_connection">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="660" as="sourcePoint" />
            <mxPoint x="450" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多模型融合流程 -->
        <mxCell id="edge13" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="residual_connection" target="mlp_prediction">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="660" as="sourcePoint" />
            <mxPoint x="770" y="655" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge14" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mlp_prediction" target="prior_fusion">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="655" as="sourcePoint" />
            <mxPoint x="920" y="655" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="prior_fusion" target="final_fusion">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="990" y="680" as="sourcePoint" />
            <mxPoint x="910" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="800" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend1" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="50" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend2" value="天线节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="130" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend3" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="210" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend4" value="处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="290" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend5" value="融合预测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="370" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 边关系可视化 -->
        <mxCell id="edge_tt" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#6c8ebf;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="250" as="sourcePoint" />
            <mxPoint x="530" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_tt_label" value="T-T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="505" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_ta" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#d6b656;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="250" as="sourcePoint" />
            <mxPoint x="570" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_ta_label" value="T-A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="545" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_at" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#d6b656;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="250" as="sourcePoint" />
            <mxPoint x="610" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at_label" value="A-T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="585" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_aa" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;dashed=1;dashPattern=1 4;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="250" as="sourcePoint" />
            <mxPoint x="650" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_aa_label" value="A-A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="625" y="255" width="20" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
