<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="GATv2多头注意力机制" id="gatv2-multihead-attention">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="GATv2多头注意力机制与信息聚合过程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="369" height="30" as="geometry" />
        </mxCell>

        <!-- 输入层：异构图节点特征 -->
        <mxCell id="input_section" value="输入层：异构图节点特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="200" height="40" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node" value="标签节点 $\mathbf{h}_i^{tag}$&#xa;RSSI差分特征" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="80" y="150" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node" value="天线节点 $\mathbf{h}_j^{antenna}$&#xa;空间位置特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="180" y="150" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 多头注意力计算模块 -->
        <mxCell id="multihead_section" value="多头注意力计算 (H=4个头)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="80" width="200" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力头1 -->
        <mxCell id="head1" value="Head 1&#xa;$\alpha_{ij}^{(1)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="320" y="150" width="60" height="50" as="geometry" />
        </mxCell>

        <!-- 注意力头2 -->
        <mxCell id="head2" value="Head 2&#xa;$\alpha_{ij}^{(2)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="390" y="150" width="60" height="50" as="geometry" />
        </mxCell>

        <!-- 注意力头3 -->
        <mxCell id="head3" value="Head 3&#xa;$\alpha_{ij}^{(3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="460" y="150" width="60" height="50" as="geometry" />
        </mxCell>

        <!-- 注意力头4 -->
        <mxCell id="head4" value="Head 4&#xa;$\alpha_{ij}^{(4)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="530" y="150" width="60" height="50" as="geometry" />
        </mxCell>

        <!-- GATv2注意力权重计算公式 -->
        <mxCell id="attention_formula" value="注意力权重计算：&#xa;$\alpha_{ij}^{(h)} = \frac{\exp(\text{LeakyReLU}(\mathbf{a}_h^T \cdot \mathbf{W}_h [\mathbf{h}_i \| \mathbf{h}_j \| \mathbf{e}_{ij}]))}{\sum_{k \in \mathcal{N}_i} \exp(\text{LeakyReLU}(\mathbf{a}_h^T \cdot \mathbf{W}_h [\mathbf{h}_i \| \mathbf{h}_k \| \mathbf{e}_{ik}]))}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="320" y="220" width="270" height="60" as="geometry" />
        </mxCell>

        <!-- 特征聚合层 -->
        <mxCell id="aggregation_section" value="特征聚合层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="80" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 每个头的特征输出 -->
        <mxCell id="head1_output" value="$\mathbf{h}_i^{(1)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="630" y="140" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="head2_output" value="$\mathbf{h}_i^{(2)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="680" y="140" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="head3_output" value="$\mathbf{h}_i^{(3)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="730" y="140" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="head4_output" value="$\mathbf{h}_i^{(4)}$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="780" y="140" width="40" height="30" as="geometry" />
        </mxCell>

        <!-- 拼接操作 -->
        <mxCell id="concat_operation" value="拼接操作 (Concatenation)&#xa;$\mathbf{h}_i^{(l+1)} = \text{Concat}(\mathbf{h}_i^{(1)}, \mathbf{h}_i^{(2)}, \mathbf{h}_i^{(3)}, \mathbf{h}_i^{(4)})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="630" y="200" width="190" height="50" as="geometry" />
        </mxCell>

        <!-- 信号可靠性调节 -->
        <mxCell id="reliability_section" value="信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fad7ac;strokeColor=#b46504;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="80" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 可靠性评分 -->
        <mxCell id="reliability_score" value="可靠性评分&#xa;$\rho_i = \frac{1}{1 + \sigma^2(\text{RSSI}_i)}$" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="860" y="140" width="130" height="60" as="geometry" />
        </mxCell>

        <!-- 调节后特征 -->
        <mxCell id="adjusted_features" value="调节后特征&#xa;$\mathbf{h}_{adjusted} = \mathbf{h}_i \cdot (\alpha + (1-\alpha) \cdot \rho_i)$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="850" y="220" width="170" height="50" as="geometry" />
        </mxCell>

        <!-- 残差连接 -->
        <mxCell id="residual_section" value="残差连接与层归一化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="320" width="200" height="40" as="geometry" />
        </mxCell>

        <!-- 残差连接公式 -->
        <mxCell id="residual_formula" value="$\mathbf{h}_{output} = \text{LayerNorm}(\mathbf{h}_{adjusted} + \mathbf{h}_{input})$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="380" width="240" height="40" as="geometry" />
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="output_section" value="输出：增强节点特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="460" width="200" height="40" as="geometry" />
        </mxCell>

        <!-- 最终输出特征 -->
        <mxCell id="final_output" value="$\mathbf{h}_i^{(l+1)}$ (增强特征表示)" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="430" y="520" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <!-- 输入到多头注意力 -->
        <mxCell id="edge1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="tag_node" target="head1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="180" as="sourcePoint" />
            <mxPoint x="250" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="antenna_node" target="head2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="180" as="sourcePoint" />
            <mxPoint x="330" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力到特征聚合 -->
        <mxCell id="edge3" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="head1" target="head1_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="175" as="sourcePoint" />
            <mxPoint x="430" y="125" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge4" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="head2" target="head2_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="175" as="sourcePoint" />
            <mxPoint x="500" y="125" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge5" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="head3" target="head3_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="175" as="sourcePoint" />
            <mxPoint x="570" y="125" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge6" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="head4" target="head4_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="590" y="175" as="sourcePoint" />
            <mxPoint x="640" y="125" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 特征输出到拼接 -->
        <mxCell id="edge7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.2;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head1_output" target="concat_operation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="180" as="sourcePoint" />
            <mxPoint x="700" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.4;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head2_output" target="concat_operation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="180" as="sourcePoint" />
            <mxPoint x="750" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.6;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head3_output" target="concat_operation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="180" as="sourcePoint" />
            <mxPoint x="800" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.8;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head4_output" target="concat_operation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="180" as="sourcePoint" />
            <mxPoint x="850" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 拼接到可靠性调节 -->
        <mxCell id="edge11" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="concat_operation" target="reliability_score">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="820" y="225" as="sourcePoint" />
            <mxPoint x="870" y="175" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 可靠性调节到调节后特征 -->
        <mxCell id="edge12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="reliability_score" target="adjusted_features">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="925" y="210" as="sourcePoint" />
            <mxPoint x="975" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 调节后特征到残差连接 -->
        <mxCell id="edge13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="adjusted_features" target="residual_formula">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="280" as="sourcePoint" />
            <mxPoint x="900" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 残差连接到输出 -->
        <mxCell id="edge14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="residual_formula" target="output_section">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="430" as="sourcePoint" />
            <mxPoint x="550" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出到最终特征 -->
        <mxCell id="edge15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="output_section" target="final_output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="510" as="sourcePoint" />
            <mxPoint x="550" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="620" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend1" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="50" y="650" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend2" value="天线节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="130" y="650" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend3" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="210" y="650" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend4" value="处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="290" y="650" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 关键创新点标注 -->
        <mxCell id="innovation1" value="创新点1：&#xa;异构节点建模" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=9;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="280" width="100" height="40" as="geometry" />
        </mxCell>

        <mxCell id="innovation2" value="创新点2：&#xa;多头并行计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=9;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="290" width="100" height="40" as="geometry" />
        </mxCell>

        <mxCell id="innovation3" value="创新点3：&#xa;信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=9;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="290" width="100" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
