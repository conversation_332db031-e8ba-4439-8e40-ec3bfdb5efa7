<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="GATv2多头注意力机制与信息聚合过程" id="gatv2-multihead-attention">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 标题 -->
        <mxCell id="title" value="GATv2多头注意力机制与信息聚合过程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="350" y="20" width="469" height="30" as="geometry" />
        </mxCell>

        <!-- 输入：异构图节点 -->
        <mxCell id="input_nodes" value="异构图节点输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="70" y="140" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node" value="天线节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="150" y="140" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 动态拓扑生成 -->
        <mxCell id="topology_generation" value="动态拓扑生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="80" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- K近邻算法 -->
        <mxCell id="knn_algorithm" value="自适应K近邻" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="300" y="140" width="110" height="40" as="geometry" />
        </mxCell>

        <!-- 边权重计算 -->
        <mxCell id="edge_weights" value="边权重计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="300" y="200" width="110" height="40" as="geometry" />
        </mxCell>

        <!-- GATv2多头注意力层 -->
        <mxCell id="multihead_attention" value="GATv2多头注意力层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fad7ac;strokeColor=#b46504;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="80" width="180" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力头1 -->
        <mxCell id="head1" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="480" y="140" width="50" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力头2 -->
        <mxCell id="head2" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="540" y="140" width="50" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力头3 -->
        <mxCell id="head3" value="Head 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="600" y="140" width="50" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力头4 -->
        <mxCell id="head4" value="Head 4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="660" y="140" width="50" height="30" as="geometry" />
        </mxCell>

        <!-- 特征聚合 -->
        <mxCell id="feature_aggregation" value="特征聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="540" y="200" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 信号可靠性调节 -->
        <mxCell id="reliability_adjustment" value="信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="750" y="80" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 可靠性评分 -->
        <mxCell id="reliability_score" value="可靠性评分" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fad7ac;strokeColor=#b46504;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="770" y="140" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 残差连接与层归一化 -->
        <mxCell id="residual_layernorm" value="残差连接与层归一化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="750" y="200" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- 输出特征 -->
        <mxCell id="output_features" value="增强节点特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="300" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 核心公式展示 -->
        <mxCell id="attention_formula" value="GATv2注意力权重计算公式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="380" width="200" height="30" as="geometry" />
        </mxCell>

        <mxCell id="formula_content" value="α_ij = softmax(LeakyReLU(a^T · W[h_i || h_j || e_ij]))" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="300" height="30" as="geometry" />
        </mxCell>

        <mxCell id="knn_formula" value="动态K近邻公式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="470" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="knn_content" value="K_i = max(K_min, min(K_max, K_base + β·log(1+ρ_i)))" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="510" width="280" height="30" as="geometry" />
        </mxCell>

        <mxCell id="reliability_formula" value="信号可靠性调节公式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="560" width="160" height="30" as="geometry" />
        </mxCell>

        <mxCell id="reliability_content" value="h_adj = h_i · (α + (1-α) · ρ_i)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="600" width="200" height="30" as="geometry" />
        </mxCell>

        <!-- 主要连接线 -->
        <!-- 输入到动态拓扑 -->
        <mxCell id="edge1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="input_nodes" target="topology_generation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="100" as="sourcePoint" />
            <mxPoint x="280" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 动态拓扑到多头注意力 -->
        <mxCell id="edge2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="topology_generation" target="multihead_attention">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="100" as="sourcePoint" />
            <mxPoint x="500" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力到信号可靠性调节 -->
        <mxCell id="edge3" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="multihead_attention" target="reliability_adjustment">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="680" y="100" as="sourcePoint" />
            <mxPoint x="750" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力头到特征聚合 -->
        <mxCell id="edge4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="head2" target="feature_aggregation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="565" y="170" as="sourcePoint" />
            <mxPoint x="580" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 信号可靠性调节到残差连接 -->
        <mxCell id="edge5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="reliability_adjustment" target="residual_layernorm">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="120" as="sourcePoint" />
            <mxPoint x="810" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 特征聚合和残差连接到输出 -->
        <mxCell id="edge6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="feature_aggregation" target="output_features">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="240" as="sourcePoint" />
            <mxPoint x="525" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="680" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend1" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="50" y="710" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="legend2" value="天线节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="130" y="710" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="legend3" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="210" y="710" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="legend4" value="处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="290" y="710" width="60" height="25" as="geometry" />
        </mxCell>

        <!-- 关键创新点标注 -->
        <mxCell id="innovation1" value="创新点1：&#xa;动态拓扑生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="260" width="80" height="50" as="geometry" />
        </mxCell>

        <mxCell id="innovation2" value="创新点2：&#xa;多头注意力机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="260" width="80" height="50" as="geometry" />
        </mxCell>

        <mxCell id="innovation3" value="创新点3：&#xa;信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#ff0000;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="810" y="260" width="80" height="50" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

        <!-- 阶段4和5的连接 -->
        <mxCell id="edge10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;" edge="1" parent="1" source="layer3_output" target="stage4_title">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="805" y="530" as="sourcePoint" />
            <mxPoint x="225" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 可靠性调节流程 -->
        <mxCell id="edge11" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="reliability_calculation" target="feature_adjustment">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="660" as="sourcePoint" />
            <mxPoint x="240" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge12" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="feature_adjustment" target="residual_connection">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="660" as="sourcePoint" />
            <mxPoint x="450" y="660" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多模型融合流程 -->
        <mxCell id="edge13" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="residual_connection" target="mlp_prediction">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="660" as="sourcePoint" />
            <mxPoint x="770" y="655" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge14" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mlp_prediction" target="prior_fusion">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="655" as="sourcePoint" />
            <mxPoint x="920" y="655" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="prior_fusion" target="final_fusion">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="990" y="680" as="sourcePoint" />
            <mxPoint x="910" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="800" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend1" value="标签节点" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="50" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend2" value="天线节点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="130" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend3" value="注意力头" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="210" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend4" value="处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="290" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend5" value="融合预测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="370" y="830" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 边关系可视化 -->
        <mxCell id="edge_tt" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#6c8ebf;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="250" as="sourcePoint" />
            <mxPoint x="530" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_tt_label" value="T-T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="505" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_ta" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#d6b656;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="250" as="sourcePoint" />
            <mxPoint x="570" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_ta_label" value="T-A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="545" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_at" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#d6b656;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="250" as="sourcePoint" />
            <mxPoint x="610" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at_label" value="A-T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="585" y="255" width="20" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_aa" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;dashed=1;dashPattern=1 4;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="250" as="sourcePoint" />
            <mxPoint x="650" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_aa_label" value="A-A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="625" y="255" width="20" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
